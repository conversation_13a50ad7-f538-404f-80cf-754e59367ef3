{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**/*", "!node_modules/**", "!dist/**"]}, "formatter": {"enabled": true, "indentStyle": "tab", "lineWidth": 160}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noArrayIndexKey": "off"}, "complexity": {"noForEach": "off"}, "style": {"useImportType": "off", "noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}, "a11y": {"useMediaCaption": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "assist": {"actions": {"source": {"organizeImports": "on"}}}}